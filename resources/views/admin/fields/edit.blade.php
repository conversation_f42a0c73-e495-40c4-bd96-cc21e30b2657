@extends('layouts.admin')

@section('title', 'Edit Field - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Edit Field - {{ $field->name }}</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.fields.index') }}">Fields</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">Edit Field Information</div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('admin.fields.show', $field) }}" class="btn btn-sm btn-info">
                            <i class="ri-eye-line me-1"></i>View Field
                        </a>
                        <a href="{{ route('admin.fields.index') }}" class="btn btn-sm btn-secondary">
                            <i class="ri-arrow-left-line me-1"></i>Back to Fields
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.fields.update', $field) }}">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Basic Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Field Name -->
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Field Name <span
                                                    class="text-danger">*</span></label>
                                            <input type="text" name="name" id="name"
                                                value="{{ old('name', $field->name) }}" required
                                                class="form-control @error('name') is-invalid @enderror"
                                                placeholder="e.g., Soccer Field A">
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Field Icon -->
                                        <x-icon-selector
                                            :selected="old('icon', $field->icon ?? 'ri-football-line')"
                                            name="icon"
                                            :required="false"
                                            label="Field Icon"
                                            id="field-edit-icon-selector"
                                        />

                                        <!-- Field Type -->
                                        <div class="mb-3">
                                            <label for="type" class="form-label">Field Type <span
                                                    class="text-danger">*</span></label>
                                            <select name="type" id="type" required
                                                class="form-select @error('type') is-invalid @enderror">
                                                <option value="">Select Field Type</option>
                                                @foreach (\App\Models\Field::getFieldTypes() as $key => $type)
                                                    <option value="{{ $key }}"
                                                        {{ old('type', $field->type) === $key ? 'selected' : '' }}>
                                                        {{ $type }}</option>
                                                @endforeach
                                            </select>
                                            @error('type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- min_booking_hours -->
                                        <div class="mb-3">
                                            <label for="min_booking_hours" class="form-label">Minimum booking_hours <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="min_booking_hours" id="min_booking_hours"
                                                value="{{ old('min_booking_hours', $field->min_booking_hours) }}"
                                                step="0.5" min="0" max="9999.99" required
                                                class="form-control @error('min_booking_hours') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('min_booking_hours')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- max_booking_hours -->
                                        <div class="mb-3">
                                            <label for="max_booking_hours" class="form-label">Maximum booking hours <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="max_booking_hours" id="max_booking_hours"
                                                value="{{ old('max_booking_hours', $field->max_booking_hours) }}"
                                                step="0.5" min="0" max="9999.99" required
                                                class="form-control @error('max_booking_hours') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('max_booking_hours')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Description -->
                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea name="description" id="description" rows="3"
                                                class="form-control @error('description') is-invalid @enderror"
                                                placeholder="Describe the field features and specifications...">{{ old('description', $field->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!--  indicating vacation and maintenance dates -->
                                        <!---------------------------------------------------------->
                                        <div class="col-xl-12">
                                            <label for="start_time1" class="form-label">
                                                Vacation or maintenance period
                                            </label>
                                        </div>
                                        <div class="col-xl-3">
                                            <label for="begin_date" class="form-label">
                                                Start Date
                                            </label>
                                            <input type="date" name="begin_date" id="begin_date"
                                                value="{{ old('begin_date', $field->start_date) }}" class="form-control">
                                        </div>

                                        <div class="col-xl-3">
                                            <label for="end_date" class="form-label">
                                                End Date
                                            </label>
                                            <input type="date" name="end_date" id="end_date"
                                                value="{{ old('end_date', $field->end_date) }}" class="form-control">
                                        </div>

                                        <!------------------------------------------------>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing and Capacity -->
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">Pricing & Capacity</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Day Hourly Rate -->
                                        <div class="mb-3">
                                            <label for="hourly_rate" class="form-label">Day Hourly Rate (XCG) <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="hourly_rate" id="hourly_rate"
                                                value="{{ old('hourly_rate', $field->hourly_rate) }}" step="0.01"
                                                min="0" max="9999.99" required
                                                class="form-control @error('hourly_rate') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('hourly_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Night Hourly Rate -->
                                        <div class="mb-3">
                                            <label for="night_hourly_rate" class="form-label">Night Hourly Rate
                                                (XCG)</label>
                                            <input type="number" name="night_hourly_rate" id="night_hourly_rate"
                                                value="{{ old('night_hourly_rate', $field->night_hourly_rate) }}"
                                                step="0.01" min="0" max="9999.99"
                                                class="form-control @error('night_hourly_rate') is-invalid @enderror"
                                                placeholder="0.00">
                                            @error('night_hourly_rate')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Leave blank to use day rate for all hours</div>
                                        </div>

                                        <!-- Night Time Start -->
                                        <div class="mb-3">
                                            <label for="night_time_start" class="form-label">Night Time Starts At</label>
                                            <input type="time" name="night_time_start" id="night_time_start"
                                                value="{{ old('night_time_start', $field->night_time_start ? (is_string($field->night_time_start) ? (strlen($field->night_time_start) === 5 ? $field->night_time_start : \Carbon\Carbon::parse($field->night_time_start)->format('H:i')) : $field->night_time_start->format('H:i')) : '18:00') }}"
                                                class="form-control @error('night_time_start') is-invalid @enderror">
                                            @error('night_time_start')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Time when night rate pricing begins (default: 6:00 PM)
                                            </div>
                                        </div>

                                        <!-- Capacity -->
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">Maximum Capacity <span
                                                    class="text-danger">*</span></label>
                                            <input type="number" name="capacity" id="capacity"
                                                value="{{ old('capacity', $field->capacity) }}" min="1"
                                                max="1000" required
                                                class="form-control @error('capacity') is-invalid @enderror"
                                                placeholder="Number of people">
                                            @error('capacity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Status -->
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status <span
                                                    class="text-danger">*</span></label>
                                            <select name="status" id="status" required
                                                class="form-select @error('status') is-invalid @enderror">
                                                @foreach (\App\Models\Field::getStatuses() as $key => $status)
                                                    <option value="{{ $key }}"
                                                        {{ old('status', $field->status) === $key ? 'selected' : '' }}>
                                                        {{ $status }}</option>
                                                @endforeach
                                            </select>
                                            @error('status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <!-- Field Statistics -->
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading">Field Statistics</h6>
                                            <p class="mb-1">Total Bookings:
                                                <strong>{{ $field->bookings()->count() }}</strong>
                                            </p>
                                            <p class="mb-1">Active Bookings:
                                                <strong>{{ $field->activeBookings()->count() }}</strong>
                                            </p>
                                            <p class="mb-0">Created:
                                                <strong>{{ $field->created_at->format('M d, Y') }}</strong>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Amenities -->
                        <!--<div class="row mt-3">
                                                        <div class="col-12">
                                                            <div class="card">
                                                                <div class="card-header">
                                                                    <h5 class="card-title mb-0">Available Amenities</h5>
                                                                </div>
                                                                <div class="card-body">
                                                                    <div class="row">
                                                                        @forelse(\App\Models\Amenity::active()->orderBy('name')->get() as $amenity)
    <div class="col-md-3 col-sm-6">
                                                                                <div class="form-check">
                                                                                    <input type="checkbox" name="amenities[]"
                                                                                        value="{{ $amenity->id }}"
                                                                                        {{ ($field->amenities && $field->amenities->contains($amenity->id)) || in_array($amenity->id, old('amenities', [])) ? 'checked' : '' }}
                                                                                        class="form-check-input" id="amenity_{{ $amenity->id }}">
                                                                                    <label class="form-check-label"
                                                                                        for="amenity_{{ $amenity->id }}">
                                                                                        <i
                                                                                            class="{{ $amenity->icon_class }} me-1"></i>{{ $amenity->name }}
                                                                                    </label>
                                                                                </div>
                                                                            </div>
                                            @empty
                                                                            <div class="col-12">
                                                                                <div class="alert alert-info">
                                                                                    <p class="mb-0">No amenities available. <a
                                                                                            href="{{ route('admin.amenities.create') }}">Create some
                                                                                            amenities</a> first.</p>
                                                                                </div>
                                                                            </div>
    @endforelse
                                                                    </div>
                                                                    @error('amenities')
        <div class="text-danger mt-2">{{ $message }}</div>
    @enderror
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>-->

                        <!-- Utilities -->
                        <!--<div class="row mt-3">
                                                    <div class="col-12">
                                                        <div class="card">
                                                            <div class="card-header">
                                                                <h5 class="card-title mb-0">Available Utilities</h5>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    @forelse(\App\Models\Utility::active()->orderBy('name')->get() as $utility)
    <div class="col-md-3 col-sm-6">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="utilities[]"
                                                                                    value="{{ $utility->id }}"
                                                                                    {{ ($field->utilities && $field->utilities->contains($utility->id)) || in_array($utility->id, old('utilities', [])) ? 'checked' : '' }}
                                                                                    class="form-check-input" id="utility_{{ $utility->id }}">
                                                                                <label class="form-check-label"
                                                                                    for="utility_{{ $utility->id }}">
                                                                                    <i
                                                                                        class="{{ $utility->icon_class ?? 'ri-tools-line' }} me-1"></i>{{ $utility->name }}
                                                                                </label>
                                                                            </div>
                                                                        </div>
                                            @empty
                                                                        <div class="col-12">
                                                                            <div class="alert alert-info">
                                                                                <p class="mb-0">No utilities available. <a
                                                                                        href="{{ route('admin.utilities.create') }}">Create some
                                                                                        utilities</a> first.</p>
                                                                            </div>
                                                                        </div>
    @endforelse
                                                                </div>
                                                                @error('utilities')
        <div class="text-danger mt-2">{{ $message }}</div>
    @enderror
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>-->

                        <!-- Form Actions -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ri-save-line me-1"></i>Update Field
                                        </button>
                                        <a href="{{ route('admin.fields.show', $field) }}" class="btn btn-secondary">
                                            <i class="ri-eye-line me-1"></i>View Field
                                        </a>
                                    </div>
                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal"
                                        data-bs-target="#deleteModal">
                                        <i class="ri-delete-bin-line me-1"></i>Delete Field
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Delete Field</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="ri-error-warning-line text-danger" style="font-size: 3rem;"></i>
                        </div>
                        <p>Are you sure you want to delete <strong>{{ $field->name }}</strong>?</p>
                        <p class="text-muted">This action cannot be undone.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.fields.destroy', $field) }}" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Field</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
